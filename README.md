# PDF to Word Converter
Batch PDF to Word conversion tool

⚠️ **Security Notice**: This project has been updated with dependency fixes to address known security vulnerabilities. It is recommended to use the updated requirements.txt file.

## Security Improvements
- Updated all dependencies to secure versions
- Added file validation and size limits
- Improved error handling and logging
- Prevented path traversal attacks

## Direct Installation

Windows [Download Link](https://github.com/Shanyaliux/pdf2word/releases/download/1.0.0/pdf2word.exe)

## Build from Source

```shell
# Recommended to use Python 3.8+
pip install -r requirements.txt
pyinstaller -F -w pdf2word.py
```

## Customization

Clone the repository to modify and compile

```shell
git clone https://github.com/Shanyaliux/pdf2word.git
```

> To modify the UI interface, install `qtdesigner` to edit `MainWindow.ui` and compile
