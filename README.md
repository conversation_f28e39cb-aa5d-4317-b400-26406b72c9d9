# PDF转word
可实现批量pdf转word

⚠️ **安全提示**: 此项目已更新依赖项以修复已知安全漏洞。建议使用更新后的requirements.txt文件。

## 安全改进
- 更新了所有依赖项到安全版本
- 添加了文件验证和大小限制
- 改进了错误处理和日志记录
- 防止路径遍历攻击

## 直接安装

windows [下载地址](https://github.com/Shanyaliux/pdf2word/releases/download/1.0.0/pdf2word.exe)

## 源码编译

```shell
# 推荐使用Python 3.8+
pip install -r requirements.txt
pyinstaller -F -w pdf2word.py
```

## 自定义

克隆仓库进行修改并编译即可

```shell
git clone https://github.com/Shanyaliux/pdf2word.git
```

> 若想要对UI界面进行修改，则自行安装`qtdesigner`修改`MainWindow.ui`并编译
