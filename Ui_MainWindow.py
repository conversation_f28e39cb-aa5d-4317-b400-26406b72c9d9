# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'e:\Code\Python\pdf2word\MainWindow.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(800, 600)
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.gridLayout = QtWidgets.QGridLayout(self.centralwidget)
        self.gridLayout.setObjectName("gridLayout")
        self.widget_2 = QtWidgets.QWidget(self.centralwidget)
        self.widget_2.setObjectName("widget_2")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.widget_2)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.selectFilesButton = QtWidgets.QPushButton(self.widget_2)
        self.selectFilesButton.setObjectName("selectFilesButton")
        self.horizontalLayout.addWidget(self.selectFilesButton)
        self.selectOutputButton = QtWidgets.QPushButton(self.widget_2)
        self.selectOutputButton.setObjectName("selectOutputButton")
        self.horizontalLayout.addWidget(self.selectOutputButton)
        self.outputPathEditText = QtWidgets.QLineEdit(self.widget_2)
        self.outputPathEditText.setReadOnly(True)
        self.outputPathEditText.setObjectName("outputPathEditText")
        self.horizontalLayout.addWidget(self.outputPathEditText)
        self.startButton = QtWidgets.QPushButton(self.widget_2)
        self.startButton.setObjectName("startButton")
        self.horizontalLayout.addWidget(self.startButton)
        self.gridLayout.addWidget(self.widget_2, 0, 0, 1, 1)
        self.progressBar = QtWidgets.QProgressBar(self.centralwidget)
        self.progressBar.setProperty("value", 24)
        self.progressBar.setObjectName("progressBar")
        self.gridLayout.addWidget(self.progressBar, 6, 0, 1, 1)
        self.filesPathEditText = QtWidgets.QTextEdit(self.centralwidget)
        self.filesPathEditText.setLineWrapMode(QtWidgets.QTextEdit.NoWrap)
        self.filesPathEditText.setReadOnly(True)
        self.filesPathEditText.setObjectName("filesPathEditText")
        self.gridLayout.addWidget(self.filesPathEditText, 3, 0, 1, 2)
        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QtWidgets.QStatusBar(MainWindow)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "pdf2word"))
        self.selectFilesButton.setText(_translate("MainWindow", "选择文件"))
        self.selectOutputButton.setText(_translate("MainWindow", "选择输出目录"))
        self.startButton.setText(_translate("MainWindow", "开始转换"))
