import os
import threading
from PyQt5 import QtWidgets, QtCore
from Ui_MainWindow import Ui_MainWindow
from pdf2docx import Converter
from PyQt5.QtWidgets import *
import logging


class MainWindow(QtWidgets.QMainWindow, Ui_MainWindow):
    def __init__(self, parent=None):
        super(MainWindow, self).__init__(parent)
        self.setupUi(self)


        self.startButton.setEnabled(False)
        self.progressBar.setVisible(False)
        self.progressBar.setMinimum(0)
        self.progressBar.setFormat('%v\%m')


        # Bind click events
        self.selectFilesButton.clicked.connect(self.openFilesDialog)
        self.selectOutputButton.clicked.connect(self.openOutputPath)
        self.startButton.clicked.connect(self.startConvert)


    def openFilesDialog(self):
        self.files, _ = QFileDialog.getOpenFileNames(self, "Select PDF Files", r"./", "PDF Files(*.pdf)")
        if len(self.files) != 0:
            # Validate selected files
            valid_files = []
            for file_path in self.files:
                if self.validate_pdf_file(file_path):
                    valid_files.append(file_path)
                else:
                    logging.warning(f"Invalid or potentially unsafe file: {file_path}")

            if valid_files:
                self.files = valid_files
                self.filesPathEditText.setText("\n".join(self.files))
                self.startButton.setEnabled(True)
                self.outputPath = os.path.dirname(self.files[0])
                self.outputPathEditText.setText(self.outputPath)
            else:
                QMessageBox.warning(self, "Warning", "No valid PDF files selected")

    def validate_pdf_file(self, file_path):
        """Validate if the file is a safe PDF file"""
        try:
            # Check if file exists and is readable
            if not os.path.isfile(file_path):
                return False

            # Check file extension
            if not file_path.lower().endswith('.pdf'):
                return False

            # Check file size (prevent extremely large files)
            file_size = os.path.getsize(file_path)
            if file_size > 100 * 1024 * 1024:  # 100MB limit
                return False

            # Basic path traversal protection
            normalized_path = os.path.normpath(file_path)
            if '..' in normalized_path:
                return False

            return True
        except Exception as e:
            logging.error(f"Error validating file {file_path}: {e}")
            return False


    def openOutputPath(self):
        self.outputPath = QFileDialog.getExistingDirectory(self, "Select Output Folder", r"./")
        self.outputPathEditText.setText(self.outputPath)


    def startConvert(self):
        self.startButton.setEnabled(False)
        self.selectFilesButton.setEnabled(False)
        self.selectOutputButton.setEnabled(False)
        self.progressBar.setMaximum(len(self.files))
        self.progressBar.setVisible(True)
        self.progressBar.setValue(0)
        self.pdf2docxThread = Pdf2docxThread()
        self.pdf2docxThread.setFilesAndOutputPath(self.files, self.outputPath)
        self.pdf2docxThread.progressBarValue.connect(self.progressbarSignal2Value)
        self.statusbar.showMessage("Starting conversion, please wait...")
        # QMessageBox.information(self,'Info','Starting conversion, please wait...', QMessageBox.Yes | QMessageBox.Yes)
        self.pdf2docxThread.start()
        

    def progressbarSignal2Value(self, i):
        self.progressBar.setValue(i)
        if i == len(self.files):
            self.startButton.setEnabled(True)
            self.selectFilesButton.setEnabled(True)
            self.selectOutputButton.setEnabled(True)
            self.statusbar.showMessage("Conversion completed", 5000)
            self.progressBar.setVisible(False)
            QMessageBox.information(self,'Info','Conversion completed', QMessageBox.Yes | QMessageBox.Yes)
            
            
    
        
class Pdf2docxThread (QtCore.QThread):   
    progressBarValue = QtCore.pyqtSignal(int)
    def __init__(self):
        super(Pdf2docxThread, self).__init__()
        
    def setFilesAndOutputPath(self, files, outputPath):
        self.files = files
        self.outputPath = outputPath

    def run(self):
        for count, pdf in enumerate(self.files, start=1):
            _, tempfilename = os.path.split(pdf)
            filename, _ = os.path.splitext(tempfilename)
            docxFile = os.path.join(self.outputPath, f'{filename}.docx')
            self.pdf2docx(pdf, docxFile)
            self.progressBarValue.emit(count)
        
    def pdf2docx(self, pdfFile, docxFile):
        """Convert PDF to DOCX with error handling"""
        try:
            # Create converter instance (object)
            cv = Converter(pdfFile)
            # Start conversion
            cv.convert(docxFile)
            # Conversion completed, release instance (object)
            cv.close()
            logging.info(f"Successfully converted {pdfFile} to {docxFile}")
        except Exception as e:
            logging.error(f"Error converting {pdfFile}: {e}")
            # Ensure converter is closed even if conversion fails
            try:
                cv.close()
            except:
                pass
            raise e





