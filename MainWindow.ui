<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>PDF to Word Converter</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <widget class="QWidget" name="widget_2" native="true">
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QPushButton" name="selectFilesButton">
         <property name="text">
          <string>Select Files</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="selectOutputButton">
         <property name="text">
          <string>Select Output Directory</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="outputPathEditText">
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="startButton">
         <property name="text">
          <string>Start Conversion</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item row="6" column="0">
     <widget class="QProgressBar" name="progressBar">
      <property name="value">
       <number>24</number>
      </property>
     </widget>
    </item>
    <item row="3" column="0" colspan="2">
     <widget class="QTextEdit" name="filesPathEditText">
      <property name="lineWrapMode">
       <enum>QTextEdit::NoWrap</enum>
      </property>
      <property name="readOnly">
       <bool>true</bool>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
